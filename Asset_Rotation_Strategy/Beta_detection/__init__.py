"""
Beta Detection System
A comprehensive cryptocurrency beta analysis system for measuring asset volatility relative to Bitcoin.

Based on TradingView implementation with enhanced features:
- Configurable analysis modes (quick, standard, comprehensive)
- Multiple asset groups (DeFi, meme coins, Layer 1s)
- Rich table displays with ROC indicators
- JSON result storage and comparison
- Command-line interface

Main Components:
- BetaCalculator: Core beta calculation logic
- BetaTableDisplay: Rich table formatting and display
- BetaDetectionRunner: Main orchestration runner
- BetaConfigManager: Configuration management

Usage:
    from Beta_detection import BetaDetectionRunner
    
    runner = BetaDetectionRunner()
    results = runner.run_beta_analysis()

Command Line:
    python Beta_detection/run_beta_analysis.py --help
"""

from .beta_calculator import BetaCalculator
from .beta_table_display import BetaTableDisplay
from .beta_runner import BetaDetectionRunner
from .beta_config_manager import BetaConfigManager

__version__ = "1.0.0"
__author__ = "Asset Screener Team"
__description__ = "Cryptocurrency Beta Detection System"

# Package metadata
__all__ = [
    'BetaCalculator',
    'BetaTableDisplay', 
    'BetaDetectionRunner',
    'BetaConfigManager'
]

# Default configuration
DEFAULT_MEASUREMENT_LENGTH = 100
DEFAULT_BENCHMARK = "BTC/USDT"
DEFAULT_ROC_THRESHOLD = 0.05

def quick_analysis(symbols=None, since=None):
    """
    Quick beta analysis with default settings.
    
    Args:
        symbols: List of symbols to analyze (optional)
        since: Start date for analysis (optional)
        
    Returns:
        Dictionary of beta results
    """
    runner = BetaDetectionRunner()
    return runner.run_beta_analysis(
        symbols=symbols,
        since=since,
        analysis_mode='quick',
        show_previous_comparison=False
    )
