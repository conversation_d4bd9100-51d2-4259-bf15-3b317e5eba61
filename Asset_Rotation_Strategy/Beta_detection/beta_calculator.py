"""
Beta Detection System - Core Beta Calculator
Based on TradingView implementation for asset beta calculation against BTC benchmark.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BetaCalculator:
    """
    Calculate beta values for assets against BTC benchmark.
    Based on TradingView implementation with focus on BTC as primary benchmark.
    """
    
    def __init__(self, measurement_length: int = 100):
        """
        Initialize beta calculator.
        
        Args:
            measurement_length: Number of days for beta calculation (default: 100)
        """
        self.measurement_length = measurement_length
        self.benchmark_symbol = "BTC/USDT"  # Primary benchmark (BTC only)
        
    def calculate_return_percent(self, prices: pd.Series) -> pd.Series:
        """
        Calculate percentage returns from price series.
        Equivalent to TradingView's return_percent function.
        
        Args:
            prices: Price series (close prices)
            
        Returns:
            Percentage returns series
        """
        return prices.pct_change() * 100
    
    def calculate_beta(self, asset_prices: pd.Series, benchmark_prices: pd.Series) -> float:
        """
        Calculate beta value for an asset against benchmark.
        Implements TradingView's beta calculation logic.
        
        Args:
            asset_prices: Asset price series
            benchmark_prices: Benchmark price series
            
        Returns:
            Beta value
        """
        # Calculate returns
        asset_returns = self.calculate_return_percent(asset_prices)
        benchmark_returns = self.calculate_return_percent(benchmark_prices)
        
        # Ensure we have enough data
        if len(asset_returns) < self.measurement_length:
            logger.warning(f"Insufficient data for beta calculation. Need {self.measurement_length}, got {len(asset_returns)}")
            return np.nan
        
        # Get the last measurement_length periods
        asset_returns = asset_returns.tail(self.measurement_length)
        benchmark_returns = benchmark_returns.tail(self.measurement_length)
        
        # Remove NaN values
        valid_data = pd.DataFrame({
            'asset': asset_returns,
            'benchmark': benchmark_returns
        }).dropna()
        
        if len(valid_data) < self.measurement_length * 0.8:  # Need at least 80% of data
            logger.warning(f"Too many NaN values in data for beta calculation")
            return np.nan
        
        asset_returns_clean = valid_data['asset']
        benchmark_returns_clean = valid_data['benchmark']
        
        # Calculate averages
        avg_asset_return = asset_returns_clean.mean()
        avg_benchmark_return = benchmark_returns_clean.mean()
        
        # Calculate covariance manually (as in TradingView)
        covariance_sum = 0.0
        length = len(asset_returns_clean)
        
        for i in range(length):
            asset_variance = asset_returns_clean.iloc[i] - avg_asset_return
            benchmark_variance = benchmark_returns_clean.iloc[i] - avg_benchmark_return
            covariance_sum += asset_variance * benchmark_variance
        
        covariance = covariance_sum / (length - 1)
        
        # Calculate benchmark variance
        benchmark_variance = benchmark_returns_clean.var(ddof=1)
        
        if benchmark_variance == 0:
            logger.warning("Benchmark variance is zero, cannot calculate beta")
            return np.nan
        
        # Calculate beta
        beta = covariance / benchmark_variance
        
        return beta
    
    def calculate_beta_change_direction(self, current_beta: float, previous_beta: float, 
                                      threshold: float = 0.05) -> str:
        """
        Calculate beta change direction with arrows.
        Implements TradingView's arrow direction logic.
        
        Args:
            current_beta: Current beta value
            previous_beta: Previous beta value
            threshold: Threshold for significant change (default: 0.05)
            
        Returns:
            Arrow direction string
        """
        if pd.isna(previous_beta) or pd.isna(current_beta):
            return "→"
        
        change = current_beta - previous_beta
        
        if change > threshold:
            return "↑"
        elif change < -threshold:
            return "↓"
        elif change > 0:
            return "→"
        else:
            return "←"
    
    def calculate_multiple_betas(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        Calculate beta values for multiple assets against BTC benchmark.
        
        Args:
            data_dict: Dictionary with symbol as key and DataFrame with OHLCV data as value
            
        Returns:
            Dictionary with beta calculations for each asset
        """
        results = {}
        
        # Get benchmark data
        if self.benchmark_symbol not in data_dict:
            logger.error(f"Benchmark symbol {self.benchmark_symbol} not found in data")
            return results
        
        benchmark_data = data_dict[self.benchmark_symbol]
        benchmark_prices = benchmark_data['close']
        
        logger.info(f"Calculating betas against {self.benchmark_symbol} for {len(data_dict)} assets")
        
        for symbol, df in data_dict.items():
            if symbol == self.benchmark_symbol:
                continue  # Skip benchmark itself
            
            try:
                asset_prices = df['close']
                
                # Align data by index (dates)
                aligned_data = pd.DataFrame({
                    'asset': asset_prices,
                    'benchmark': benchmark_prices
                }).dropna()
                
                if len(aligned_data) < self.measurement_length:
                    logger.warning(f"Insufficient aligned data for {symbol}")
                    continue
                
                # Calculate beta
                beta = self.calculate_beta(aligned_data['asset'], aligned_data['benchmark'])
                
                if not pd.isna(beta):
                    results[symbol] = {
                        'beta': beta,
                        'measurement_length': self.measurement_length,
                        'data_points': len(aligned_data),
                        'last_update': datetime.now().isoformat()
                    }
                    logger.info(f"Beta for {symbol}: {beta:.3f}")
                else:
                    logger.warning(f"Could not calculate beta for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error calculating beta for {symbol}: {e}")
                continue
        
        return results

    def calculate_multiple_betas_with_daily_roc(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """
        Calculate beta values for multiple assets with daily ROC comparison.
        Compares today's beta (last 100 days ending today) with yesterday's beta (last 100 days ending yesterday).

        Args:
            data_dict: Dictionary with symbol as key and DataFrame with OHLCV data as value

        Returns:
            Dictionary with beta calculations and daily ROC for each asset
        """
        results = {}

        # Get benchmark data
        if self.benchmark_symbol not in data_dict:
            logger.error(f"Benchmark symbol {self.benchmark_symbol} not found in data")
            return results

        benchmark_data = data_dict[self.benchmark_symbol]
        benchmark_prices = benchmark_data['close']

        logger.info(f"Calculating betas with daily ROC against {self.benchmark_symbol} for {len(data_dict)} assets")

        for symbol, df in data_dict.items():
            if symbol == self.benchmark_symbol:
                continue

            try:
                asset_prices = df['close']

                # Align data by index (dates)
                aligned_data = pd.DataFrame({
                    'asset': asset_prices,
                    'benchmark': benchmark_prices
                }).dropna()

                if len(aligned_data) < self.measurement_length + 1:  # Need extra day for yesterday's calculation
                    logger.warning(f"Insufficient aligned data for {symbol} (need {self.measurement_length + 1}, got {len(aligned_data)})")
                    continue

                # Calculate today's beta (using data up to the last available date)
                today_data = aligned_data.tail(self.measurement_length)
                today_beta = self.calculate_beta(today_data['asset'], today_data['benchmark'])

                # Calculate yesterday's beta (using data up to one day before the last available date)
                yesterday_data = aligned_data.iloc[:-1].tail(self.measurement_length)  # Exclude last day, then take last measurement_length
                yesterday_beta = self.calculate_beta(yesterday_data['asset'], yesterday_data['benchmark'])

                if not pd.isna(today_beta):
                    # Calculate ROC arrow
                    roc_arrow = self.calculate_beta_change_direction(today_beta, yesterday_beta)

                    results[symbol] = {
                        'beta': today_beta,
                        'previous_beta': yesterday_beta if not pd.isna(yesterday_beta) else None,
                        'roc_arrow': roc_arrow,
                        'measurement_length': self.measurement_length,
                        'data_points': len(today_data),
                        'last_update': datetime.now().isoformat()
                    }

                    if not pd.isna(yesterday_beta):
                        change = today_beta - yesterday_beta
                        logger.info(f"Beta for {symbol}: {today_beta:.3f} (yesterday: {yesterday_beta:.3f}, change: {change:+.3f}, {roc_arrow})")
                    else:
                        logger.info(f"Beta for {symbol}: {today_beta:.3f} (no yesterday data)")
                else:
                    logger.warning(f"Could not calculate beta for {symbol}")

            except Exception as e:
                logger.error(f"Error calculating beta for {symbol}: {e}")
                continue

        return results
    
    def get_binance_asset_list(self, category: str = "all") -> List[str]:
        """
        Get Binance asset list by category.

        Args:
            category: Asset category ("all", "top_50", "major", "defi", "meme", "layer1", etc.)

        Returns:
            List of Binance asset symbols
        """
        # Major cryptocurrencies (Top tier by market cap)
        major_assets = [
            "BTC/USDT", "ETH/USDT", "SOL/USDT", "XRP/USDT", "BNB/USDT",
            "ADA/USDT", "AVAX/USDT", "DOT/USDT", "LTC/USDT", "LINK/USDT"
        ]

        # Top 50 by volume (most liquid)
        top_50_assets = [
            "BTC/USDT", "ETH/USDT", "SOL/USDT", "XRP/USDT", "DOGE/USDT",
            "WIF/USDT", "BNB/USDT", "ADA/USDT", "WLD/USDT", "ENA/USDT",
            "CETUS/USDT", "AVAX/USDT", "FET/USDT", "RUNE/USDT", "HBAR/USDT",
            "LINK/USDT", "NEIRO/USDT", "APT/USDT", "LTC/USDT", "S/USDT",
            "VIRTUAL/USDT", "TAO/USDT", "BONK/USDT", "INJ/USDT", "CRV/USDT",
            "COOKIE/USDT", "ARB/USDT", "AAVE/USDT", "PNUT/USDT", "SEI/USDT",
            "NEAR/USDT", "TIA/USDT", "BCH/USDT", "TRX/USDT", "JUP/USDT",
            "ONDO/USDT", "MUBARAK/USDT", "PENGU/USDT", "UNI/USDT", "EIGEN/USDT",
            "DOT/USDT", "GALA/USDT", "ATOM/USDT", "KAITO/USDT", "FLOKI/USDT",
            "SHIB/USDT", "BOME/USDT", "OP/USDT", "PYTH/USDT", "OM/USDT"
        ]

        # DeFi focused assets
        defi_assets = [
            "AAVE/USDT", "UNI/USDT", "LINK/USDT", "CRV/USDT", "MKR/USDT",
            "LDO/USDT", "PENDLE/USDT", "JUP/USDT", "RAY/USDT", "ORCA/USDT",
            "GMX/USDT", "ENS/USDT", "COW/USDT", "CAKE/USDT", "RUNE/USDT",
            "OSMO/USDT", "CETUS/USDT", "BLUR/USDT", "DYDX/USDT", "SYN/USDT"
        ]

        # Meme coins
        meme_assets = [
            "DOGE/USDT", "SHIB/USDT", "PEPE/USDT", "WIF/USDT", "BONK/USDT",
            "FLOKI/USDT", "BOME/USDT", "NEIRO/USDT", "PNUT/USDT", "PENGU/USDT",
            "1MBABYDOGE/USDT", "1000SATS/USDT", "1000CAT/USDT", "1000CHEEMS/USDT",
            "HMSTR/USDT", "DOGS/USDT", "NOT/USDT", "TURBO/USDT", "MEME/USDT", "ACT/USDT"
        ]

        # Layer 1 blockchains
        layer1_assets = [
            "ETH/USDT", "SOL/USDT", "ADA/USDT", "AVAX/USDT", "DOT/USDT",
            "ATOM/USDT", "SUI/USDT", "APT/USDT", "NEAR/USDT", "TRX/USDT",
            "STX/USDT", "SEI/USDT", "INJ/USDT", "TON/USDT", "ICP/USDT",
            "FIL/USDT", "VET/USDT", "THETA/USDT", "EOS/USDT", "NEO/USDT"
        ]

        # AI & Computing
        ai_assets = [
            "TAO/USDT", "FET/USDT", "WLD/USDT", "ARKM/USDT", "IO/USDT",
            "CGPT/USDT", "AIXBT/USDT"
        ]

        # All Binance assets (175 total)
        all_assets = [
            # Major cryptocurrencies
            "BTC/USDT", "ETH/USDT", "SOL/USDT", "XRP/USDT", "BNB/USDT",
            "ADA/USDT", "AVAX/USDT", "DOT/USDT", "LTC/USDT", "LINK/USDT",

            # Layer 1 Blockchains
            "SUI/USDT", "APT/USDT", "NEAR/USDT", "ATOM/USDT", "TRX/USDT",
            "STX/USDT", "SEI/USDT", "INJ/USDT", "TON/USDT", "ICP/USDT",
            "FIL/USDT", "VET/USDT", "THETA/USDT", "EOS/USDT", "NEO/USDT",
            "XTZ/USDT", "IOTA/USDT", "EGLD/USDT", "KAIA/USDT", "CFX/USDT",

            # Layer 2 & Scaling
            "ARB/USDT", "OP/USDT", "POL/USDT", "ZK/USDT", "STRK/USDT", "MANTA/USDT",

            # DeFi Tokens
            "AAVE/USDT", "UNI/USDT", "CRV/USDT", "MKR/USDT", "LDO/USDT",
            "PENDLE/USDT", "JUP/USDT", "RAY/USDT", "ORCA/USDT", "GMX/USDT",
            "ENS/USDT", "COW/USDT", "CAKE/USDT", "RUNE/USDT", "OSMO/USDT", "CETUS/USDT",

            # AI & Computing
            "TAO/USDT", "FET/USDT", "WLD/USDT", "ARKM/USDT", "IO/USDT",
            "CGPT/USDT", "AIXBT/USDT",

            # Meme Coins
            "DOGE/USDT", "SHIB/USDT", "PEPE/USDT", "WIF/USDT", "BONK/USDT",
            "FLOKI/USDT", "BOME/USDT", "NEIRO/USDT", "PNUT/USDT", "PENGU/USDT",
            "1MBABYDOGE/USDT", "1000SATS/USDT", "1000CAT/USDT", "1000CHEEMS/USDT",
            "HMSTR/USDT", "DOGS/USDT", "NOT/USDT", "TURBO/USDT", "MEME/USDT",
            "ACT/USDT", "BABY/USDT",

            # Gaming & Metaverse
            "GALA/USDT", "SAND/USDT", "MANA/USDT", "APE/USDT", "YGG/USDT",
            "TLM/USDT", "CHZ/USDT", "BIGTIME/USDT", "PIXEL/USDT", "BEAMX/USDT",
            "BANANA/USDT", "EPIC/USDT",

            # Infrastructure & Oracles
            "PYTH/USDT", "API3/USDT", "TRB/USDT", "HBAR/USDT", "QNT/USDT", "AR/USDT",

            # Stablecoins & Fiat
            "FDUSD/USDT", "EUR/USDT",

            # Exchange & Utility
            "CRO/USDT", "OKB/USDT",

            # Emerging Projects
            "ENA/USDT", "EIGEN/USDT", "ETHFI/USDT", "REZ/USDT", "ONDO/USDT",
            "JTO/USDT", "W/USDT", "ZRO/USDT", "SAGA/USDT", "OMNI/USDT",
            "ALT/USDT", "VANA/USDT", "MOVE/USDT", "USUAL/USDT", "BIO/USDT",
            "LAYER/USDT", "KERNEL/USDT", "WCT/USDT", "BERA/USDT", "KAITO/USDT",
            "VIRTUAL/USDT", "S/USDT", "COOKIE/USDT", "MUBARAK/USDT", "TRUMP/USDT",

            # Additional assets
            "OM/USDT", "RSR/USDT", "BLUR/USDT", "DYDX/USDT", "AUCTION/USDT",
            "SYN/USDT", "FORM/USDT", "SHELL/USDT", "PARTI/USDT", "CVC/USDT",
            "GUN/USDT", "CKB/USDT", "THE/USDT", "RED/USDT", "SLF/USDT",
            "CATI/USDT", "HIVE/USDT", "TNSR/USDT", "ONT/USDT", "RPL/USDT",
            "ZEN/USDT", "HEI/USDT", "ACX/USDT", "BANANAS31/USDT", "CHESS/USDT",
            "VELODROME/USDT", "STEEM/USDT", "GPS/USDT", "UTK/USDT", "T/USDT",
            "JUV/USDT", "IDEX/USDT", "DF/USDT", "ALGO/USDT", "XLM/USDT",
            "PEOPLE/USDT", "BCH/USDT", "TIA/USDT", "NIL/USDT", "PHA/USDT",
            "TST/USDT", "RARE/USDT", "BB/USDT", "BROCCOLI714/USDT", "TUT/USDT",
            "BMT/USDT", "ANIME/USDT", "VANRY/USDT", "ORDI/USDT"
        ]

        # Return based on category
        category_map = {
            "all": all_assets,
            "major": major_assets,
            "top_50": top_50_assets,
            "defi": defi_assets,
            "meme": meme_assets,
            "layer1": layer1_assets,
            "ai": ai_assets
        }

        return category_map.get(category, all_assets)

    def get_asset_list_from_tradingview(self) -> List[str]:
        """
        Get the original TradingView asset list for backward compatibility.
        Now returns a subset of Binance assets that match the original TradingView selection.
        """
        # Original TradingView-style selection using Binance assets
        tradingview_style_assets = [
            "SOL/USDT", "ETH/USDT", "DOGE/USDT", "SHIB/USDT", "PEPE/USDT",
            "LINK/USDT", "TRX/USDT", "AAVE/USDT", "BONK/USDT", "XLM/USDT",
            "STX/USDT", "ATOM/USDT", "CRO/USDT", "MANA/USDT", "WIF/USDT",
            "FLOKI/USDT", "GALA/USDT", "ADA/USDT", "AVAX/USDT", "DOT/USDT",
            "UNI/USDT", "SUI/USDT", "APT/USDT", "ARB/USDT", "NEAR/USDT",
            "INJ/USDT", "SEI/USDT", "FET/USDT", "TAO/USDT", "OKB/USDT"
        ]

        return tradingview_style_assets
