/@version=5
indicator("{MC} BETA Table {Dual Benchmark} Edgaras", overlay = true)

// Parameters
//TICKERS and Benchmarks are hardcoded
length = input(100, "Beta measurement length", group = "Settings", tooltip = "Beta measurment length refers to the number of days the asset (TICKER) is being measured against the benchmark")


benchmark_01_define = input.string("INDEX:BTCUSD")
benchmark_02_define = input.string("INDEX:ETHUSD")

return_percent(src) =>
     ta.change(src) * 100 / src[1]

benchmark_01   = request.security(benchmark_01_define, 'D', close)
benchmark_02   = request.security(benchmark_02_define, 'D', close)

// Input TICKERS
asset_01 = input.string("BITMART:TOSHIUSDT")
asset_02 = input.string("CRYPTO:SOLUSD")
asset_03 = input.string("CRYPTO:ETHUSD")
asset_04 = input.string("CRYPTO:DOGEUSD")
asset_05 = input.string("CRYPTO:SCUSD")
asset_06 = input.string("GATEIO:DOGUSDT")
asset_07 = input.string("CRYPTO:SHIBUSD")
asset_08 = input.string("CRYPTO:PEPEUSD")
asset_09 = input.string("BINANCE:LQTYUSDT")
asset_10 = input.string("BITGET:SLERFUSDT")
asset_11 = input.string("COINBASE:LINKUSD")
asset_12 = input.string("MEXC:WIFUSDT")
asset_13 = input.string("CRYPTO:PONKEUSD")
asset_14 = input.string("BINANCE:MANAUSDT")
asset_15 = input.string("MEXC:POPCATUSDT.P")
asset_16 = input.string("UNISWAP:CONANWETH_10BFF0.USD")
asset_17 = input.string("BINANCE:TRXUSDT")
asset_18 = input.string("CRYPTO:PEPEUSD")
asset_19 = input.string("BINANCE:AAVEUSDT")
asset_20 = input.string("COINBASE:AEROUSD")
asset_21 = input.string("BITMART:DADDYUSDT")
asset_22 = input.string("CRYPTO:XMRUSD")
asset_23 = input.string("COINBASE:RNDRUSD")
asset_24 = input.string("MEXC:FWOGUSDT")
asset_25 = input.string("BINANCE:BONKUSDT")
asset_26 = input.string("COINBASE:XLMUSD")
asset_27 = input.string("COINBASE:STXUSD")
asset_28 = input.string("COINBASE:ATOMUSD")
asset_29 = input.string("CRYPTO:OKBUSD")
asset_30 = input.string("CRYPTO:CROUSD")

//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------

// {Benchmark 1} & {Benchmark 2} & {TICKER} Beta Calculations  

// Ticker data requests
asset_01_instrument = request.security(asset_01, '1D', close)
asset_02_instrument = request.security(asset_02, '1D', close)
asset_03_instrument = request.security(asset_03, '1D', close)
asset_04_instrument = request.security(asset_04, '1D', close)
asset_05_instrument = request.security(asset_05, '1D', close)
asset_06_instrument = request.security(asset_06, '1D', close)
asset_07_instrument = request.security(asset_07, '1D', close)
asset_08_instrument = request.security(asset_08, '1D', close)
asset_09_instrument = request.security(asset_09, '1D', close)
asset_10_instrument = request.security(asset_10, '1D', close)
asset_11_instrument = request.security(asset_11, '1D', close)
asset_12_instrument = request.security(asset_12, '1D', close)
asset_13_instrument = request.security(asset_13, '1D', close)
asset_14_instrument = request.security(asset_14, '1D', close)
asset_15_instrument = request.security(asset_15, '1D', close)
asset_16_instrument = request.security(asset_16, '1D', close)
asset_17_instrument = request.security(asset_17, '1D', close)
asset_18_instrument = request.security(asset_18, '1D', close)
asset_19_instrument = request.security(asset_19, '1D', close)
asset_20_instrument = request.security(asset_20, '1D', close)
asset_21_instrument = request.security(asset_21, '1D', close)
asset_22_instrument = request.security(asset_22, '1D', close)
asset_23_instrument = request.security(asset_23, '1D', close)
asset_24_instrument = request.security(asset_24, '1D', close)
asset_25_instrument = request.security(asset_25, '1D', close)
asset_26_instrument = request.security(asset_26, '1D', close)
asset_27_instrument = request.security(asset_27, '1D', close)
asset_28_instrument = request.security(asset_28, '1D', close)
asset_29_instrument = request.security(asset_29, '1D', close)
asset_30_instrument = request.security(asset_30, '1D', close)

// Benchmark benchmark_01 Beta calculation
calculate_beta_benchmark_01(benchmark_01, asset) =>
    inst_return = return_percent(asset)
    bench_return = return_percent(benchmark_01)
    avg_inst_return = ta.sma(inst_return, length)
    avg_bench_return = ta.sma(bench_return, length)

    sum = 0.0
    for idx = 0 to length - 1
        inst_variance = inst_return[idx] - avg_inst_return
        bench_variance = bench_return[idx] - avg_bench_return
        sum += inst_variance * bench_variance

    covariance = sum / (length - 1)
    beta = covariance / ta.variance(bench_return, length)

// Benchmark benchmark_02 Beta calculation
calculate_beta_benchmark_02(benchmark_02, asset) =>
    inst_return = return_percent(asset)
    bench_return = return_percent(benchmark_02)
    avg_inst_return = ta.sma(inst_return, length)
    avg_bench_return = ta.sma(bench_return, length)

    sum = 0.0
    for idx = 0 to length - 1
        inst_variance = inst_return[idx] - avg_inst_return
        bench_variance = bench_return[idx] - avg_bench_return
        sum += inst_variance * bench_variance

    covariance = sum / (length - 1)
    beta = covariance / ta.variance(bench_return, length)

// Ticker/Benchmark1/Benchmark2 Beta Calculations
asset_01_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_01_instrument)
asset_01_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_01_instrument)

asset_02_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_02_instrument)
asset_02_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_02_instrument)

asset_03_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_03_instrument)
asset_03_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_03_instrument)

asset_04_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_04_instrument)
asset_04_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_04_instrument)

asset_05_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_05_instrument)
asset_05_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_05_instrument)

asset_06_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_06_instrument)
asset_06_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_06_instrument)

asset_07_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_07_instrument)
asset_07_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_07_instrument)

asset_08_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_08_instrument)
asset_08_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_08_instrument)

asset_09_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_09_instrument)
asset_09_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_09_instrument)

asset_10_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_10_instrument)
asset_10_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_10_instrument)

asset_11_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_11_instrument)
asset_11_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_11_instrument)

asset_12_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_12_instrument)
asset_12_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_12_instrument)

asset_13_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_13_instrument)
asset_13_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_13_instrument)

asset_14_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_14_instrument)
asset_14_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_14_instrument)

asset_15_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_15_instrument)
asset_15_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_15_instrument)

asset_16_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_16_instrument)
asset_16_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_16_instrument)

asset_17_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_17_instrument)
asset_17_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_17_instrument)

asset_18_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_18_instrument)
asset_18_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_18_instrument)

asset_19_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_19_instrument)
asset_19_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_19_instrument)

asset_20_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_20_instrument)
asset_20_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_20_instrument)

asset_21_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_21_instrument)
asset_21_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_21_instrument)

asset_22_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_22_instrument)
asset_22_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_22_instrument)

asset_23_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_23_instrument)
asset_23_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_23_instrument)

asset_24_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_24_instrument)
asset_24_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_24_instrument)

asset_25_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_25_instrument)
asset_25_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_25_instrument)

asset_26_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_26_instrument)
asset_26_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_26_instrument)

asset_27_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_27_instrument)
asset_27_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_27_instrument)

asset_28_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_28_instrument)
asset_28_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_28_instrument)

asset_29_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_29_instrument)
asset_29_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_29_instrument)

asset_30_benchmark_01_beta = calculate_beta_benchmark_01(benchmark_01, asset_30_instrument)
asset_30_benchmark_02_beta = calculate_beta_benchmark_02(benchmark_02, asset_30_instrument)

//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------

// Arrow Directions {Beta Table}

// {benchmark_01} Store the previous TICKER Beta values
var float prev_asset_01_benchmark_01_beta = na
var float prev_asset_02_benchmark_01_beta = na
var float prev_asset_03_benchmark_01_beta = na
var float prev_asset_04_benchmark_01_beta = na
var float prev_asset_05_benchmark_01_beta = na
var float prev_asset_06_benchmark_01_beta = na
var float prev_asset_07_benchmark_01_beta = na
var float prev_asset_08_benchmark_01_beta = na
var float prev_asset_09_benchmark_01_beta = na
var float prev_asset_10_benchmark_01_beta = na
var float prev_asset_11_benchmark_01_beta = na
var float prev_asset_12_benchmark_01_beta = na
var float prev_asset_13_benchmark_01_beta = na
var float prev_asset_14_benchmark_01_beta = na
var float prev_asset_15_benchmark_01_beta = na
var float prev_asset_16_benchmark_01_beta = na
var float prev_asset_17_benchmark_01_beta = na
var float prev_asset_18_benchmark_01_beta = na
var float prev_asset_19_benchmark_01_beta = na
var float prev_asset_20_benchmark_01_beta = na
var float prev_asset_21_benchmark_01_beta = na
var float prev_asset_22_benchmark_01_beta = na
var float prev_asset_23_benchmark_01_beta = na
var float prev_asset_24_benchmark_01_beta = na
var float prev_asset_25_benchmark_01_beta = na
var float prev_asset_26_benchmark_01_beta = na
var float prev_asset_27_benchmark_01_beta = na
var float prev_asset_28_benchmark_01_beta = na
var float prev_asset_29_benchmark_01_beta = na
var float prev_asset_30_benchmark_01_beta = na

// {benchmark_01} Define a function to determine the arrow direction
benchmark_01_arrow_direction(benchmark_01_beta, prev_benchmark_01_beta) =>
    if (not na(prev_benchmark_01_beta) and benchmark_01_beta > prev_benchmark_01_beta)
        if benchmark_01_beta - prev_benchmark_01_beta > 0.05
            "↑"
        else
            "→"
    else
        if (not na(prev_benchmark_01_beta) and prev_benchmark_01_beta - benchmark_01_beta > 0.05)
            "↓"
        else
            "←"

// {benchmark_01} Usage for different tokens
asset_01_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_01_benchmark_01_beta, prev_asset_01_benchmark_01_beta)
asset_02_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_02_benchmark_01_beta, prev_asset_02_benchmark_01_beta)
asset_03_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_03_benchmark_01_beta, prev_asset_03_benchmark_01_beta)
asset_04_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_04_benchmark_01_beta, prev_asset_04_benchmark_01_beta)
asset_05_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_05_benchmark_01_beta, prev_asset_05_benchmark_01_beta)
asset_06_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_06_benchmark_01_beta, prev_asset_06_benchmark_01_beta)
asset_07_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_07_benchmark_01_beta, prev_asset_07_benchmark_01_beta)
asset_08_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_08_benchmark_01_beta, prev_asset_08_benchmark_01_beta)
asset_09_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_09_benchmark_01_beta, prev_asset_09_benchmark_01_beta)
asset_10_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_10_benchmark_01_beta, prev_asset_10_benchmark_01_beta)
asset_11_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_11_benchmark_01_beta, prev_asset_11_benchmark_01_beta)
asset_12_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_12_benchmark_01_beta, prev_asset_12_benchmark_01_beta)
asset_13_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_13_benchmark_01_beta, prev_asset_13_benchmark_01_beta)
asset_14_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_14_benchmark_01_beta, prev_asset_14_benchmark_01_beta)
asset_15_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_15_benchmark_01_beta, prev_asset_15_benchmark_01_beta)
asset_16_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_16_benchmark_01_beta, prev_asset_16_benchmark_01_beta)
asset_17_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_17_benchmark_01_beta, prev_asset_17_benchmark_01_beta)
asset_18_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_18_benchmark_01_beta, prev_asset_18_benchmark_01_beta)
asset_19_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_19_benchmark_01_beta, prev_asset_19_benchmark_01_beta)
asset_20_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_20_benchmark_01_beta, prev_asset_20_benchmark_01_beta)
asset_21_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_21_benchmark_01_beta, prev_asset_21_benchmark_01_beta)
asset_22_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_22_benchmark_01_beta, prev_asset_22_benchmark_01_beta)
asset_23_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_23_benchmark_01_beta, prev_asset_23_benchmark_01_beta)
asset_24_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_24_benchmark_01_beta, prev_asset_24_benchmark_01_beta)
asset_25_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_25_benchmark_01_beta, prev_asset_25_benchmark_01_beta)
asset_26_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_26_benchmark_01_beta, prev_asset_26_benchmark_01_beta)
asset_27_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_27_benchmark_01_beta, prev_asset_27_benchmark_01_beta)
asset_28_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_28_benchmark_01_beta, prev_asset_28_benchmark_01_beta)
asset_29_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_29_benchmark_01_beta, prev_asset_29_benchmark_01_beta)
asset_30_benchmark_01_arrow_direction = benchmark_01_arrow_direction(asset_30_benchmark_01_beta, prev_asset_30_benchmark_01_beta)

// {benchmark_01} Update the previous beta values
prev_asset_01_benchmark_01_beta := asset_01_benchmark_01_beta
prev_asset_02_benchmark_01_beta := asset_02_benchmark_01_beta
prev_asset_03_benchmark_01_beta := asset_03_benchmark_01_beta
prev_asset_04_benchmark_01_beta := asset_04_benchmark_01_beta
prev_asset_05_benchmark_01_beta := asset_05_benchmark_01_beta
prev_asset_06_benchmark_01_beta := asset_06_benchmark_01_beta
prev_asset_07_benchmark_01_beta := asset_07_benchmark_01_beta
prev_asset_08_benchmark_01_beta := asset_08_benchmark_01_beta
prev_asset_09_benchmark_01_beta := asset_09_benchmark_01_beta
prev_asset_10_benchmark_01_beta := asset_10_benchmark_01_beta
prev_asset_11_benchmark_01_beta := asset_11_benchmark_01_beta
prev_asset_12_benchmark_01_beta := asset_12_benchmark_01_beta
prev_asset_13_benchmark_01_beta := asset_13_benchmark_01_beta
prev_asset_14_benchmark_01_beta := asset_14_benchmark_01_beta
prev_asset_15_benchmark_01_beta := asset_15_benchmark_01_beta
prev_asset_16_benchmark_01_beta := asset_16_benchmark_01_beta
prev_asset_17_benchmark_01_beta := asset_17_benchmark_01_beta
prev_asset_18_benchmark_01_beta := asset_18_benchmark_01_beta
prev_asset_19_benchmark_01_beta := asset_19_benchmark_01_beta
prev_asset_20_benchmark_01_beta := asset_20_benchmark_01_beta
prev_asset_21_benchmark_01_beta := asset_21_benchmark_01_beta
prev_asset_22_benchmark_01_beta := asset_22_benchmark_01_beta
prev_asset_23_benchmark_01_beta := asset_23_benchmark_01_beta
prev_asset_24_benchmark_01_beta := asset_24_benchmark_01_beta
prev_asset_25_benchmark_01_beta := asset_25_benchmark_01_beta
prev_asset_26_benchmark_01_beta := asset_26_benchmark_01_beta
prev_asset_27_benchmark_01_beta := asset_27_benchmark_01_beta
prev_asset_28_benchmark_01_beta := asset_28_benchmark_01_beta
prev_asset_29_benchmark_01_beta := asset_29_benchmark_01_beta
prev_asset_30_benchmark_01_beta := asset_30_benchmark_01_beta

// {benchmark_02} Store the previous TICKER Beta values
var float prev_asset_01_benchmark_02_beta = na
var float prev_asset_02_benchmark_02_beta = na
var float prev_asset_03_benchmark_02_beta = na
var float prev_asset_04_benchmark_02_beta = na
var float prev_asset_05_benchmark_02_beta = na
var float prev_asset_06_benchmark_02_beta = na
var float prev_asset_07_benchmark_02_beta = na
var float prev_asset_08_benchmark_02_beta = na
var float prev_asset_09_benchmark_02_beta = na
var float prev_asset_10_benchmark_02_beta = na
var float prev_asset_11_benchmark_02_beta = na
var float prev_asset_12_benchmark_02_beta = na
var float prev_asset_13_benchmark_02_beta = na
var float prev_asset_14_benchmark_02_beta = na
var float prev_asset_15_benchmark_02_beta = na
var float prev_asset_16_benchmark_02_beta = na
var float prev_asset_17_benchmark_02_beta = na
var float prev_asset_18_benchmark_02_beta = na
var float prev_asset_19_benchmark_02_beta = na
var float prev_asset_20_benchmark_02_beta = na
var float prev_asset_21_benchmark_02_beta = na
var float prev_asset_22_benchmark_02_beta = na
var float prev_asset_23_benchmark_02_beta = na
var float prev_asset_24_benchmark_02_beta = na
var float prev_asset_25_benchmark_02_beta = na
var float prev_asset_26_benchmark_02_beta = na
var float prev_asset_27_benchmark_02_beta = na
var float prev_asset_28_benchmark_02_beta = na
var float prev_asset_29_benchmark_02_beta = na
var float prev_asset_30_benchmark_02_beta = na

// {benchmark_02} Define a function to determine the arrow direction
benchmark_02_arrow_direction(benchmark_02_beta, prev_benchmark_02_beta) =>
    if (not na(prev_benchmark_02_beta) and benchmark_02_beta > prev_benchmark_02_beta)
        if benchmark_02_beta - prev_benchmark_02_beta > 0.05
            "↑"
        else
            "→"
    else
        if (not na(prev_benchmark_02_beta) and prev_benchmark_02_beta - benchmark_02_beta > 0.05)
            "↓"
        else
            "←"

// {benchmark_02} Usage for different tokens
asset_01_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_01_benchmark_02_beta, prev_asset_01_benchmark_02_beta)
asset_02_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_02_benchmark_02_beta, prev_asset_02_benchmark_02_beta)
asset_03_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_03_benchmark_02_beta, prev_asset_03_benchmark_02_beta)
asset_04_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_04_benchmark_02_beta, prev_asset_04_benchmark_02_beta)
asset_05_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_05_benchmark_02_beta, prev_asset_05_benchmark_02_beta)
asset_06_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_06_benchmark_02_beta, prev_asset_06_benchmark_02_beta)
asset_07_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_07_benchmark_02_beta, prev_asset_07_benchmark_02_beta)
asset_08_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_08_benchmark_02_beta, prev_asset_08_benchmark_02_beta)
asset_09_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_09_benchmark_02_beta, prev_asset_09_benchmark_02_beta)
asset_10_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_10_benchmark_02_beta, prev_asset_10_benchmark_02_beta)
asset_11_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_11_benchmark_02_beta, prev_asset_11_benchmark_02_beta)
asset_12_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_12_benchmark_02_beta, prev_asset_12_benchmark_02_beta)
asset_13_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_13_benchmark_02_beta, prev_asset_13_benchmark_02_beta)
asset_14_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_14_benchmark_02_beta, prev_asset_14_benchmark_02_beta)
asset_15_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_15_benchmark_02_beta, prev_asset_15_benchmark_02_beta)
asset_16_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_16_benchmark_02_beta, prev_asset_16_benchmark_02_beta)
asset_17_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_17_benchmark_02_beta, prev_asset_17_benchmark_02_beta)
asset_18_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_18_benchmark_02_beta, prev_asset_18_benchmark_02_beta)
asset_19_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_19_benchmark_02_beta, prev_asset_19_benchmark_02_beta)
asset_20_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_20_benchmark_02_beta, prev_asset_20_benchmark_02_beta)
asset_21_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_21_benchmark_02_beta, prev_asset_21_benchmark_02_beta)
asset_22_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_22_benchmark_02_beta, prev_asset_22_benchmark_02_beta)
asset_23_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_23_benchmark_02_beta, prev_asset_23_benchmark_02_beta)
asset_24_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_24_benchmark_02_beta, prev_asset_24_benchmark_02_beta)
asset_25_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_25_benchmark_02_beta, prev_asset_25_benchmark_02_beta)
asset_26_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_26_benchmark_02_beta, prev_asset_26_benchmark_02_beta)
asset_27_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_27_benchmark_02_beta, prev_asset_27_benchmark_02_beta)
asset_28_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_28_benchmark_02_beta, prev_asset_28_benchmark_02_beta)
asset_29_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_29_benchmark_02_beta, prev_asset_29_benchmark_02_beta)
asset_30_benchmark_02_arrow_direction = benchmark_02_arrow_direction(asset_30_benchmark_02_beta, prev_asset_30_benchmark_02_beta)

// {benchmark_02} Update the previous beta values
prev_asset_01_benchmark_02_beta := asset_01_benchmark_02_beta
prev_asset_02_benchmark_02_beta := asset_02_benchmark_02_beta
prev_asset_03_benchmark_02_beta := asset_03_benchmark_02_beta
prev_asset_04_benchmark_02_beta := asset_04_benchmark_02_beta
prev_asset_05_benchmark_02_beta := asset_05_benchmark_02_beta
prev_asset_06_benchmark_02_beta := asset_06_benchmark_02_beta
prev_asset_07_benchmark_02_beta := asset_07_benchmark_02_beta
prev_asset_08_benchmark_02_beta := asset_08_benchmark_02_beta
prev_asset_09_benchmark_02_beta := asset_09_benchmark_02_beta
prev_asset_10_benchmark_02_beta := asset_10_benchmark_02_beta
prev_asset_11_benchmark_02_beta := asset_11_benchmark_02_beta
prev_asset_12_benchmark_02_beta := asset_12_benchmark_02_beta
prev_asset_13_benchmark_02_beta := asset_13_benchmark_02_beta
prev_asset_14_benchmark_02_beta := asset_14_benchmark_02_beta
prev_asset_15_benchmark_02_beta := asset_15_benchmark_02_beta
prev_asset_16_benchmark_02_beta := asset_16_benchmark_02_beta
prev_asset_17_benchmark_02_beta := asset_17_benchmark_02_beta
prev_asset_18_benchmark_02_beta := asset_18_benchmark_02_beta
prev_asset_19_benchmark_02_beta := asset_19_benchmark_02_beta
prev_asset_20_benchmark_02_beta := asset_20_benchmark_02_beta
prev_asset_21_benchmark_02_beta := asset_21_benchmark_02_beta
prev_asset_22_benchmark_02_beta := asset_22_benchmark_02_beta
prev_asset_23_benchmark_02_beta := asset_23_benchmark_02_beta
prev_asset_24_benchmark_02_beta := asset_24_benchmark_02_beta
prev_asset_25_benchmark_02_beta := asset_25_benchmark_02_beta
prev_asset_26_benchmark_02_beta := asset_26_benchmark_02_beta
prev_asset_27_benchmark_02_beta := asset_27_benchmark_02_beta
prev_asset_28_benchmark_02_beta := asset_28_benchmark_02_beta
prev_asset_29_benchmark_02_beta := asset_29_benchmark_02_beta
prev_asset_30_benchmark_02_beta := asset_30_benchmark_02_beta

//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------

// Beta Table code

// Initialize table
Table_def= table.new(position.middle_center, columns=10, rows= 18, border_width = 2, frame_width = 2, border_color=#04c1fb, frame_color=#04c1fb)

// Merge Cells Title
table.cell(Table_def, 0, 0, "💎 {MC} BETA Table | {Dual Benchmark} | V1 💎", text_color=color.white, bgcolor=#000000, text_halign=text.align_center)
table.merge_cells(Table_def,0,0,9,0 )

// Column 0 TICKERS
table.cell(Table_def, 0, 1,  "TICKER",  text_color=color.white,  bgcolor=#000000)
table.cell(Table_def, 0, 2,  text = asset_01, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 3,  text = asset_02, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 4,  text = asset_03, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 5,  text = asset_04, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 6,  text = asset_05, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 7,  text = asset_06, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 8,  text = asset_07, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 9,  text = asset_08, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 10, text = asset_09, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 11, text = asset_10, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 12, text = asset_11, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 13, text = asset_12, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 14, text = asset_13, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 15, text = asset_14, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 0, 16, text = asset_15, text_color=color.white, bgcolor=#000000)

// Column 1 benchmark_01 BETA
table.cell(Table_def, 1, 1, "Beta - benchmark_01", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 1, 2,  str.tostring(asset_01_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 3,  str.tostring(asset_02_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 4,  str.tostring(asset_03_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 5,  str.tostring(asset_04_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 6,  str.tostring(asset_05_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 7,  str.tostring(asset_06_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 8,  str.tostring(asset_07_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 9,  str.tostring(asset_08_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 10, str.tostring(asset_09_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 11, str.tostring(asset_10_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 12, str.tostring(asset_11_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 13, str.tostring(asset_12_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 14, str.tostring(asset_13_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 15, str.tostring(asset_14_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 1, 16, str.tostring(asset_15_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)

// Column 2 benchmark_01 ROC
table.cell(Table_def, 2, 1, "+/- ROC", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 2, 2,  " " + asset_01_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 3,  " " + asset_02_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 4,  " " + asset_03_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 5,  " " + asset_04_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 6,  " " + asset_05_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 7,  " " + asset_06_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 8,  " " + asset_07_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 9,  " " + asset_08_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 10, " " + asset_09_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 11, " " + asset_10_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 12, " " + asset_11_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 13, " " + asset_12_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 14, " " + asset_13_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 15, " " + asset_14_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 2, 16, " " + asset_15_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)

// Column 3 benchmark_02 BETA
table.cell(Table_def, 3, 1, "Beta - benchmark_02", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 3, 2,  str.tostring(asset_01_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 3,  str.tostring(asset_02_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 4,  str.tostring(asset_03_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 5,  str.tostring(asset_04_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 6,  str.tostring(asset_05_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 7,  str.tostring(asset_06_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 8,  str.tostring(asset_07_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 9,  str.tostring(asset_08_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 10, str.tostring(asset_09_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 11, str.tostring(asset_10_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 12, str.tostring(asset_11_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 13, str.tostring(asset_12_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 14, str.tostring(asset_13_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 15, str.tostring(asset_14_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 3, 16, str.tostring(asset_15_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)

// Column 4 benchmark_02 ROC
table.cell(Table_def, 4, 1, "+/- ROC", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 4, 2,  " " + asset_01_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 3,  " " + asset_02_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 4,  " " + asset_03_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 5,  " " + asset_04_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 6,  " " + asset_05_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 7,  " " + asset_06_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 8,  " " + asset_07_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 9,  " " + asset_08_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 10, " " + asset_09_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 11, " " + asset_10_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 12, " " + asset_11_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 13, " " + asset_12_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 14, " " + asset_13_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 15, " " + asset_14_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 4, 16, " " + asset_15_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)

// Column 5 TICKERS
table.cell(Table_def, 5, 1, "TICKER", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 2,  text = asset_16, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 3,  text = asset_17, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 4,  text = asset_18, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 5,  text = asset_19, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 6,  text = asset_20, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 7,  text = asset_21, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 8,  text = asset_22, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 9,  text = asset_23, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 10, text = asset_24, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 11, text = asset_25, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 12, text = asset_26, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 13, text = asset_27, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 14, text = asset_28, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 15, text = asset_29, text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 5, 16, text = asset_30, text_color=color.white, bgcolor=#000000)

// Column 6 benchmark_01 BETA
table.cell(Table_def, 6, 1, "Beta - benchmark_01", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 6, 2,  str.tostring(asset_16_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 3,  str.tostring(asset_17_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 4,  str.tostring(asset_18_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 5,  str.tostring(asset_19_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 6,  str.tostring(asset_20_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 7,  str.tostring(asset_21_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 8,  str.tostring(asset_22_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 9,  str.tostring(asset_23_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 10, str.tostring(asset_24_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 11, str.tostring(asset_25_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 12, str.tostring(asset_26_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 13, str.tostring(asset_27_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 14, str.tostring(asset_28_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 15, str.tostring(asset_29_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)
table.cell(Table_def, 6, 16, str.tostring(asset_30_benchmark_01_beta, "#.##"), text_color=#efab2d, bgcolor=#000000)

// Column 7 benchmark_01 ROC
table.cell(Table_def, 7, 1, "+/- ROC", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 7, 2,  " " + asset_16_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 3,  " " + asset_17_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 4,  " " + asset_18_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 5,  " " + asset_19_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 6,  " " + asset_20_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 7,  " " + asset_21_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 8,  " " + asset_22_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 9,  " " + asset_23_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 10, " " + asset_24_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 11, " " + asset_25_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 12, " " + asset_26_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 13, " " + asset_27_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 14, " " + asset_28_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 15, " " + asset_29_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 7, 16, " " + asset_30_benchmark_01_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)

// Column 8 benchmark_02 BETA
table.cell(Table_def, 8, 1, "Beta - benchmark_02", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 8, 2,  str.tostring(asset_16_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 3,  str.tostring(asset_17_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 4,  str.tostring(asset_18_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 5,  str.tostring(asset_19_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 6,  str.tostring(asset_20_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 7,  str.tostring(asset_21_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 8,  str.tostring(asset_22_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 9,  str.tostring(asset_23_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 10, str.tostring(asset_24_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 11, str.tostring(asset_25_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 12, str.tostring(asset_26_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 13, str.tostring(asset_27_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 14, str.tostring(asset_28_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 15, str.tostring(asset_29_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)
table.cell(Table_def, 8, 16, str.tostring(asset_30_benchmark_02_beta, "#.##"), text_color=#343de3, bgcolor=#000000)

// Column 9 benchmark_02 ROC
table.cell(Table_def, 9, 1, "+/- ROC", text_color=color.white, bgcolor=#000000)
table.cell(Table_def, 9, 2,  " " + asset_16_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 3,  " " + asset_17_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 4,  " " + asset_18_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 5,  " " + asset_19_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 6,  " " + asset_20_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 7,  " " + asset_21_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 8,  " " + asset_22_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 9,  " " + asset_23_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 10, " " + asset_24_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 11, " " + asset_25_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 12, " " + asset_26_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 13, " " + asset_27_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 14, " " + asset_28_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 15, " " + asset_29_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)
table.cell(Table_def, 9, 16, " " + asset_30_benchmark_02_arrow_direction + " ", text_color=#eeff00, bgcolor=#000000, text_halign=text.align_center)

//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------

// Trailing Beta plots

// Plotting beta values
plot(asset_01_benchmark_01_beta, 'ASSET_01 benchmark_01 Beta', color=color.new(color.orange, 3))
plot(asset_01_benchmark_02_beta, 'ASSET_01 benchmark_02 Beta', color=color.new(color.blue, 3))

plot(asset_02_benchmark_01_beta, 'ASSET_02 benchmark_01 Beta', color=color.new(color.orange, 6))
plot(asset_02_benchmark_02_beta, 'ASSET_02 benchmark_02 Beta', color=color.new(color.blue, 6))

plot(asset_03_benchmark_01_beta, 'ASSET_03 benchmark_01 Beta', color=color.new(color.orange, 9))
plot(asset_03_benchmark_02_beta, 'ASSET_03 benchmark_02 Beta', color=color.new(color.blue, 9))

plot(asset_04_benchmark_01_beta, 'ASSET_04 benchmark_01 Beta', color=color.new(color.orange, 12))
plot(asset_04_benchmark_02_beta, 'ASSET_04 benchmark_02 Beta', color=color.new(color.blue, 12))

plot(asset_05_benchmark_01_beta, 'ASSET_05 benchmark_01 Beta', color=color.new(color.orange, 15))
plot(asset_05_benchmark_02_beta, 'ASSET_05 benchmark_02 Beta', color=color.new(color.blue, 15))

plot(asset_06_benchmark_01_beta, 'ASSET_06 benchmark_01 Beta', color=color.new(color.orange, 18))
plot(asset_06_benchmark_02_beta, 'ASSET_06 benchmark_02 Beta', color=color.new(color.blue, 18))

plot(asset_07_benchmark_01_beta, 'ASSET_07 benchmark_01 Beta', color=color.new(color.orange, 21))
plot(asset_07_benchmark_02_beta, 'ASSET_07 benchmark_02 Beta', color=color.new(color.blue, 21))

plot(asset_08_benchmark_01_beta, 'ASSET_08 benchmark_01 Beta', color=color.new(color.orange, 24))
plot(asset_08_benchmark_02_beta, 'ASSET_08 benchmark_02 Beta', color=color.new(color.blue, 24))

plot(asset_09_benchmark_01_beta, 'ASSET_09 benchmark_01 Beta', color=color.new(color.orange, 27))
plot(asset_09_benchmark_02_beta, 'ASSET_09 benchmark_02 Beta', color=color.new(color.blue, 27))

plot(asset_10_benchmark_01_beta, 'ASSET_10 benchmark_01 Beta', color=color.new(color.orange, 30))
plot(asset_10_benchmark_02_beta, 'ASSET_10 benchmark_02 Beta', color=color.new(color.blue, 30))

plot(asset_11_benchmark_01_beta, 'ASSET_11 benchmark_01 Beta', color=color.new(color.orange, 33))
plot(asset_11_benchmark_02_beta, 'ASSET_11 benchmark_02 Beta', color=color.new(color.blue, 33))

plot(asset_12_benchmark_01_beta, 'ASSET_12 benchmark_01 Beta', color=color.new(color.orange, 36))
plot(asset_12_benchmark_02_beta, 'ASSET_12 benchmark_02 Beta', color=color.new(color.blue, 36))

plot(asset_13_benchmark_01_beta, 'ASSET_13 benchmark_01 Beta', color=color.new(color.orange, 39))
plot(asset_13_benchmark_02_beta, 'ASSET_13 benchmark_02 Beta', color=color.new(color.blue, 39))

plot(asset_14_benchmark_01_beta, 'ASSET_14 benchmark_01 Beta', color=color.new(color.orange, 42))
plot(asset_14_benchmark_02_beta, 'ASSET_14 benchmark_02 Beta', color=color.new(color.blue, 42))

plot(asset_15_benchmark_01_beta, 'ASSET_15 benchmark_01 Beta', color=color.new(color.orange, 45))
plot(asset_15_benchmark_02_beta, 'ASSET_15 benchmark_02 Beta', color=color.new(color.blue, 45))

plot(asset_16_benchmark_01_beta, 'ASSET_16 benchmark_01 Beta', color=color.new(color.orange, 48))
plot(asset_16_benchmark_02_beta, 'ASSET_16 benchmark_02 Beta', color=color.new(color.blue, 48))

plot(asset_17_benchmark_01_beta, 'ASSET_17 benchmark_01 Beta', color=color.new(color.orange, 51))
plot(asset_17_benchmark_02_beta, 'ASSET_17 benchmark_02 Beta', color=color.new(color.blue, 51))

plot(asset_18_benchmark_01_beta, 'ASSET_18 benchmark_01 Beta', color=color.new(color.orange, 54))
plot(asset_18_benchmark_02_beta, 'ASSET_18 benchmark_02 Beta', color=color.new(color.blue, 54))

plot(asset_19_benchmark_01_beta, 'ASSET_19 benchmark_01 Beta', color=color.new(color.orange, 57))
plot(asset_19_benchmark_02_beta, 'ASSET_19 benchmark_02 Beta', color=color.new(color.blue, 57))

plot(asset_20_benchmark_01_beta, 'ASSET_20 benchmark_01 Beta', color=color.new(color.orange, 60))
plot(asset_20_benchmark_02_beta, 'ASSET_20 benchmark_02 Beta', color=color.new(color.blue, 60))

plot(asset_21_benchmark_01_beta, 'ASSET_21 benchmark_01 Beta', color=color.new(color.orange, 63))
plot(asset_21_benchmark_02_beta, 'ASSET_21 benchmark_02 Beta', color=color.new(color.blue, 63))

plot(asset_22_benchmark_01_beta, 'ASSET_22 benchmark_01 Beta', color=color.new(color.orange, 66))
plot(asset_22_benchmark_02_beta, 'ASSET_22 benchmark_02 Beta', color=color.new(color.blue, 66))

plot(asset_23_benchmark_01_beta, 'ASSET_23 benchmark_01 Beta', color=color.new(color.orange, 69))
plot(asset_23_benchmark_02_beta, 'ASSET_23 benchmark_02 Beta', color=color.new(color.blue, 69))

plot(asset_24_benchmark_01_beta, 'ASSET_24 benchmark_01 Beta', color=color.new(color.orange, 72))
plot(asset_24_benchmark_02_beta, 'ASSET_24 benchmark_02 Beta', color=color.new(color.blue, 72))

plot(asset_25_benchmark_01_beta, 'ASSET_25 benchmark_01 Beta', color=color.new(color.orange, 75))
plot(asset_25_benchmark_02_beta, 'ASSET_25 benchmark_02 Beta', color=color.new(color.blue, 75))

plot(asset_26_benchmark_01_beta, 'ASSET_26 benchmark_01 Beta', color=color.new(color.orange, 78))
plot(asset_26_benchmark_02_beta, 'ASSET_26 benchmark_02 Beta', color=color.new(color.blue, 78))

plot(asset_27_benchmark_01_beta, 'ASSET_27 benchmark_01 Beta', color=color.new(color.orange, 81))
plot(asset_27_benchmark_02_beta, 'ASSET_27 benchmark_02 Beta', color=color.new(color.blue, 81))

plot(asset_28_benchmark_01_beta, 'ASSET_28 benchmark_01 Beta', color=color.new(color.orange, 84))
plot(asset_28_benchmark_02_beta, 'ASSET_28 benchmark_02 Beta', color=color.new(color.blue, 84))

plot(asset_29_benchmark_01_beta, 'ASSET_29 benchmark_01 Beta', color=color.new(color.orange, 87))
plot(asset_29_benchmark_02_beta, 'ASSET_29 benchmark_02 Beta', color=color.new(color.blue, 87))

plot(asset_30_benchmark_01_beta, 'ASSET_30 benchmark_01 Beta', color=color.new(color.orange, 90))
plot(asset_30_benchmark_02_beta, 'ASSET_30 benchmark_02 Beta', color=color.new(color.blue, 90))

//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------------------------------------------------------------------------

// Alert Condition Payload

alert(str.tostring("Recieved Payload") + "," + 
      str.tostring(asset_01_benchmark_01_beta) + "," + str.tostring(asset_01_benchmark_02_beta) + ";" +
      str.tostring(asset_02_benchmark_01_beta) + "," + str.tostring(asset_02_benchmark_02_beta) + ";" +
      str.tostring(asset_03_benchmark_01_beta) + "," + str.tostring(asset_03_benchmark_02_beta) + ";" +
      str.tostring(asset_04_benchmark_01_beta) + "," + str.tostring(asset_04_benchmark_02_beta) + ";" +
      str.tostring(asset_05_benchmark_01_beta) + "," + str.tostring(asset_05_benchmark_02_beta) + ";" +
      str.tostring(asset_06_benchmark_01_beta) + "," + str.tostring(asset_06_benchmark_02_beta) + ";" +
      str.tostring(asset_07_benchmark_01_beta) + "," + str.tostring(asset_07_benchmark_02_beta) + ";" +
      str.tostring(asset_08_benchmark_01_beta) + "," + str.tostring(asset_08_benchmark_02_beta) + ";" +
      str.tostring(asset_09_benchmark_01_beta) + "," + str.tostring(asset_09_benchmark_02_beta) + ";" +
      str.tostring(asset_10_benchmark_01_beta) + "," + str.tostring(asset_10_benchmark_02_beta) + ";" +
      str.tostring(asset_11_benchmark_01_beta) + "," + str.tostring(asset_11_benchmark_02_beta) + ";" +
      str.tostring(asset_12_benchmark_01_beta) + "," + str.tostring(asset_12_benchmark_02_beta) + ";" +
      str.tostring(asset_13_benchmark_01_beta) + "," + str.tostring(asset_13_benchmark_02_beta) + ";" +
      str.tostring(asset_14_benchmark_01_beta) + "," + str.tostring(asset_14_benchmark_02_beta) + ";" +
      str.tostring(asset_15_benchmark_01_beta) + "," + str.tostring(asset_15_benchmark_02_beta) + ";" +
      str.tostring(asset_16_benchmark_01_beta) + "," + str.tostring(asset_16_benchmark_02_beta) + ";" +
      str.tostring(asset_17_benchmark_01_beta) + "," + str.tostring(asset_17_benchmark_02_beta) + ";" +
      str.tostring(asset_18_benchmark_01_beta) + "," + str.tostring(asset_18_benchmark_02_beta) + ";" +
      str.tostring(asset_19_benchmark_01_beta) + "," + str.tostring(asset_19_benchmark_02_beta) + ";" +
      str.tostring(asset_20_benchmark_01_beta) + "," + str.tostring(asset_20_benchmark_02_beta) + ";" +
      str.tostring(asset_21_benchmark_01_beta) + "," + str.tostring(asset_21_benchmark_02_beta) + ";" +
      str.tostring(asset_22_benchmark_01_beta) + "," + str.tostring(asset_22_benchmark_02_beta) + ";" +
      str.tostring(asset_23_benchmark_01_beta) + "," + str.tostring(asset_23_benchmark_02_beta) + ";" +
      str.tostring(asset_24_benchmark_01_beta) + "," + str.tostring(asset_24_benchmark_02_beta) + ";" +
      str.tostring(asset_25_benchmark_01_beta) + "," + str.tostring(asset_25_benchmark_02_beta) + ";" +
      str.tostring(asset_26_benchmark_01_beta) + "," + str.tostring(asset_26_benchmark_02_beta) + ";" +
      str.tostring(asset_27_benchmark_01_beta) + "," + str.tostring(asset_27_benchmark_02_beta) + ";" +
      str.tostring(asset_28_benchmark_01_beta) + "," + str.tostring(asset_28_benchmark_02_beta) + ";" +
      str.tostring(asset_29_benchmark_01_beta) + "," + str.tostring(asset_29_benchmark_02_beta) + ";" +
      str.tostring(asset_30_benchmark_01_beta) + "," + str.tostring(asset_30_benchmark_02_beta), alert.freq_once_per_bar_close)
