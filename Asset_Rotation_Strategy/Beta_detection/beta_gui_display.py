"""
Beta Analysis GUI Display
Simple, robust GUI window for displaying beta tables with proper formatting.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from typing import Dict, Optional
import json
from datetime import datetime
import threading
import sys
import os

class BetaTableGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("💎 Beta Analysis Table | BTC Benchmark")
        self.root.geometry("1400x800")
        self.root.configure(bg='#1e1e1e')
        
        # Configure style for dark theme
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.configure_styles()
        
        # Create main frame
        self.main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create title
        self.title_label = ttk.Label(
            self.main_frame, 
            text="💎 Beta Analysis Table | BTC Benchmark 💎",
            style='Title.TLabel'
        )
        self.title_label.pack(pady=(0, 10))
        
        # Create treeview frame
        self.tree_frame = ttk.Frame(self.main_frame, style='Dark.TFrame')
        self.tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create treeview with scrollbars
        self.create_treeview()
        
        # Create stats frame
        self.stats_frame = ttk.Frame(self.main_frame, style='Dark.TFrame')
        self.stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Create control buttons
        self.button_frame = ttk.Frame(self.main_frame, style='Dark.TFrame')
        self.button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.refresh_btn = ttk.Button(
            self.button_frame, 
            text="🔄 Refresh Data", 
            command=self.refresh_data,
            style='Dark.TButton'
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.export_btn = ttk.Button(
            self.button_frame, 
            text="📊 Export CSV", 
            command=self.export_csv,
            style='Dark.TButton'
        )
        self.export_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.close_btn = ttk.Button(
            self.button_frame, 
            text="❌ Close", 
            command=self.root.quit,
            style='Dark.TButton'
        )
        self.close_btn.pack(side=tk.RIGHT)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(
            self.main_frame, 
            textvariable=self.status_var,
            style='Status.TLabel'
        )
        self.status_bar.pack(fill=tk.X, pady=(5, 0))
        
        self.current_data = None
        
    def configure_styles(self):
        """Configure dark theme styles."""
        # Dark frame
        self.style.configure('Dark.TFrame', background='#1e1e1e')
        
        # Title style
        self.style.configure(
            'Title.TLabel',
            background='#1e1e1e',
            foreground='#00d4ff',
            font=('Arial', 16, 'bold')
        )
        
        # Status style
        self.style.configure(
            'Status.TLabel',
            background='#1e1e1e',
            foreground='#888888',
            font=('Arial', 9)
        )
        
        # Button style
        self.style.configure(
            'Dark.TButton',
            background='#333333',
            foreground='#ffffff',
            borderwidth=1,
            focuscolor='none'
        )
        self.style.map('Dark.TButton',
                      background=[('active', '#444444')])
        
        # Treeview style
        self.style.configure(
            'Dark.Treeview',
            background='#2d2d2d',
            foreground='#ffffff',
            fieldbackground='#2d2d2d',
            borderwidth=0,
            font=('Consolas', 10)
        )
        self.style.configure(
            'Dark.Treeview.Heading',
            background='#404040',
            foreground='#00d4ff',
            borderwidth=1,
            font=('Arial', 11, 'bold')
        )
        self.style.map('Dark.Treeview',
                      background=[('selected', '#0078d4')])
        
    def create_treeview(self):
        """Create the treeview widget with scrollbars."""
        # Create treeview
        columns = ('ticker', 'beta', 'roc', 'category', 'volatility')
        self.tree = ttk.Treeview(
            self.tree_frame, 
            columns=columns, 
            show='headings',
            style='Dark.Treeview'
        )
        
        # Configure columns
        self.tree.heading('ticker', text='Asset Ticker')
        self.tree.heading('beta', text='Beta vs BTC')
        self.tree.heading('roc', text='ROC Change')
        self.tree.heading('category', text='Risk Category')
        self.tree.heading('volatility', text='Volatility Level')
        
        # Set column widths
        self.tree.column('ticker', width=150, anchor='center')
        self.tree.column('beta', width=120, anchor='center')
        self.tree.column('roc', width=100, anchor='center')
        self.tree.column('category', width=150, anchor='center')
        self.tree.column('volatility', width=150, anchor='center')
        
        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self.tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Configure grid weights
        self.tree_frame.grid_rowconfigure(0, weight=1)
        self.tree_frame.grid_columnconfigure(0, weight=1)
        
    def get_risk_category(self, beta: float) -> str:
        """Get risk category based on beta value."""
        if beta > 2.0:
            return "🔥 Very High Risk"
        elif beta > 1.5:
            return "🔴 High Risk"
        elif beta > 1.0:
            return "🟡 Medium Risk"
        elif beta > 0.5:
            return "🟢 Low Risk"
        else:
            return "🛡️ Very Low Risk"
    
    def get_volatility_level(self, beta: float) -> str:
        """Get volatility level description."""
        if beta > 3.0:
            return "Extremely Volatile"
        elif beta > 2.0:
            return "Highly Volatile"
        elif beta > 1.5:
            return "Moderately Volatile"
        elif beta > 1.0:
            return "Slightly Volatile"
        elif beta > 0.5:
            return "Low Volatility"
        else:
            return "Very Stable"
    
    def get_roc_arrow(self, current_beta: float, previous_beta: Optional[float]) -> str:
        """Get ROC arrow indicator."""
        if previous_beta is None:
            return "➡️ New"
        
        change = current_beta - previous_beta
        if change > 0.1:
            return "⬆️ Up"
        elif change < -0.1:
            return "⬇️ Down"
        elif change > 0:
            return "↗️ Slight Up"
        elif change < 0:
            return "↘️ Slight Down"
        else:
            return "➡️ Stable"
    
    def load_beta_data(self, beta_results: Dict[str, Dict], 
                      previous_betas: Optional[Dict[str, float]] = None):
        """Load beta data into the GUI."""
        self.current_data = beta_results
        
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Sort by beta value (descending)
        sorted_assets = sorted(beta_results.items(), 
                             key=lambda x: x[1]['beta'], 
                             reverse=True)
        
        # Add data to treeview
        for symbol, data in sorted_assets:
            beta_value = data['beta']
            prev_beta = previous_betas.get(symbol) if previous_betas else None
            
            # Format data
            ticker = symbol
            beta_str = f"{beta_value:.3f}"
            roc = self.get_roc_arrow(beta_value, prev_beta)
            category = self.get_risk_category(beta_value)
            volatility = self.get_volatility_level(beta_value)
            
            # Add color tags based on beta value
            if beta_value > 2.0:
                tag = 'high_risk'
            elif beta_value > 1.5:
                tag = 'medium_high_risk'
            elif beta_value > 1.0:
                tag = 'medium_risk'
            else:
                tag = 'low_risk'
            
            self.tree.insert('', 'end', values=(ticker, beta_str, roc, category, volatility), tags=(tag,))
        
        # Configure tags for color coding
        self.tree.tag_configure('high_risk', background='#4d1f1f', foreground='#ff6b6b')
        self.tree.tag_configure('medium_high_risk', background='#4d3d1f', foreground='#ffa500')
        self.tree.tag_configure('medium_risk', background='#3d4d1f', foreground='#ffff6b')
        self.tree.tag_configure('low_risk', background='#1f4d1f', foreground='#6bff6b')
        
        # Update stats
        self.update_stats(beta_results)
        
        # Update status
        self.status_var.set(f"Loaded {len(beta_results)} assets - Last updated: {datetime.now().strftime('%H:%M:%S')}")
    
    def update_stats(self, beta_results: Dict[str, Dict]):
        """Update statistics display."""
        # Clear existing stats
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        if not beta_results:
            return
        
        betas = [data['beta'] for data in beta_results.values()]
        
        # Calculate statistics
        total_assets = len(betas)
        avg_beta = sum(betas) / len(betas)
        min_beta = min(betas)
        max_beta = max(betas)
        
        # Count by risk categories
        very_high = sum(1 for b in betas if b > 2.0)
        high = sum(1 for b in betas if 1.5 < b <= 2.0)
        medium = sum(1 for b in betas if 1.0 < b <= 1.5)
        low = sum(1 for b in betas if b <= 1.0)
        
        # Create stats labels
        stats_text = f"📊 Total Assets: {total_assets} | 📈 Avg Beta: {avg_beta:.3f} | 🔻 Min: {min_beta:.3f} | 🔺 Max: {max_beta:.3f}"
        risk_text = f"🔥 Very High: {very_high} | 🔴 High: {high} | 🟡 Medium: {medium} | 🟢 Low: {low}"
        
        stats_label = ttk.Label(self.stats_frame, text=stats_text, style='Status.TLabel')
        stats_label.pack(anchor='w')
        
        risk_label = ttk.Label(self.stats_frame, text=risk_text, style='Status.TLabel')
        risk_label.pack(anchor='w')
    
    def refresh_data(self):
        """Refresh data by loading the latest beta results."""
        try:
            # Find the latest beta results file
            beta_dir = "Beta_detection"
            if not os.path.exists(beta_dir):
                messagebox.showerror("Error", "Beta_detection directory not found!")
                return
            
            # Get all beta result files
            files = [f for f in os.listdir(beta_dir) if f.startswith('beta_results_') and f.endswith('.json')]
            if not files:
                messagebox.showwarning("Warning", "No beta results files found!")
                return
            
            # Get the latest file
            latest_file = max(files, key=lambda f: os.path.getctime(os.path.join(beta_dir, f)))
            filepath = os.path.join(beta_dir, latest_file)
            
            # Load data
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            beta_results = data.get('results', {})
            self.load_beta_data(beta_results)
            
            messagebox.showinfo("Success", f"Data refreshed from {latest_file}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh data: {str(e)}")
    
    def export_csv(self):
        """Export current data to CSV."""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to export!")
            return
        
        try:
            # Create DataFrame
            data_list = []
            for symbol, data in self.current_data.items():
                beta_value = data['beta']
                data_list.append({
                    'Ticker': symbol,
                    'Beta': beta_value,
                    'Risk_Category': self.get_risk_category(beta_value),
                    'Volatility_Level': self.get_volatility_level(beta_value)
                })
            
            df = pd.DataFrame(data_list)
            df = df.sort_values('Beta', ascending=False)
            
            # Save to CSV
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"beta_export_{timestamp}.csv"
            df.to_csv(filename, index=False)
            
            messagebox.showinfo("Success", f"Data exported to {filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export data: {str(e)}")
    
    def show(self):
        """Show the GUI window."""
        self.root.mainloop()
    
    def close(self):
        """Close the GUI window."""
        self.root.quit()


def show_beta_gui(beta_results: Dict[str, Dict],
                 previous_betas: Optional[Dict[str, float]] = None,
                 blocking: bool = True):
    """
    Show beta results in a GUI window.

    Args:
        beta_results: Beta calculation results
        previous_betas: Previous beta values for ROC calculation
        blocking: If True, keeps the GUI open until user closes it
    """
    try:
        gui = BetaTableGUI()
        gui.load_beta_data(beta_results, previous_betas)

        if blocking:
            # Run GUI in main thread - keeps window open
            gui.show()
        else:
            # Run GUI in separate thread for non-blocking operation
            def run_gui():
                try:
                    gui.show()
                except Exception as e:
                    print(f"GUI Error: {e}")

            gui_thread = threading.Thread(target=run_gui, daemon=False)
            gui_thread.start()
            return gui_thread

    except Exception as e:
        print(f"GUI Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Test the GUI with sample data
    sample_data = {
        "BTC/USDT": {"beta": 1.000},
        "ETH/USDT": {"beta": 1.786},
        "SOL/USDT": {"beta": 1.449},
        "DOGE/USDT": {"beta": 2.264},
        "SHIB/USDT": {"beta": 1.805},
    }
    
    gui = BetaTableGUI()
    gui.load_beta_data(sample_data)
    gui.show()
