# Memecoin Discovery System Configuration
# This configuration file controls the behavior of the memecoin discovery system

# Discovery Engine Settings
discovery:
  # Maximum number of tokens to analyze per discovery cycle
  max_tokens_per_run: 50
  
  # Minimum number of social media mentions required for a token to be considered
  min_mentions_threshold: 3
  
  # How far back to look for social media mentions (in hours)
  hours_lookback: 24
  
  # Discovery cycle frequency (in minutes)
  cycle_frequency_minutes: 60
  
  # Enable/disable different discovery sources
  sources:
    twitter_enabled: true
    reddit_enabled: false  # Future implementation
    telegram_enabled: false  # Future implementation
    discord_enabled: false  # Future implementation

# Twitter Scraping Configuration (using snscrape - no API keys needed)
twitter:
  # Rate limiting settings (to be respectful)
  requests_per_minute: 20
  max_tweets_per_search: 100
  max_influencer_tweets: 5
  
  # Search parameters
  search_keywords:
    - "memecoin"
    - "meme coin"
    - "shitcoin"
    - "altcoin"
    - "new token"
    - "moon"
    - "x100"
    - "x1000"
    - "gem"
    - "launch"
    - "presale"
    - "pump"
    - "rocket"
    - "diamond hands"
    - "hodl"
    - "ape in"
    - "degen"
    - "moonshot"
    - "lambo"
    - "to the moon"
  
  # Crypto influencers to monitor
  influencers:
    - "elonmusk"
    - "VitalikButerin"
    - "cz_binance"
    - "justinsuntron"
    - "APompliano"
    - "DocumentingBTC"
    - "WhalePanda"
    - "CryptoCobain"
    - "CryptoWendyO"
    - "TheCryptoDog"
    - "CryptoBirb"
    - "CryptoCapo_"
    - "TechDev_52"
    - "CryptoCred"
    - "CryptoMichNL"
    - "CryptoHamster"
    - "CryptoKaleo"
    - "CryptoMessiah"
    - "CryptoSqueeze"
    - "CryptoTony__"

# AI Analysis Configuration
ai_analysis:
  # OpenAI model to use
  model: "gpt-4o-mini"
  
  # Alternative model for cost optimization
  fallback_model: "gpt-3.5-turbo"
  
  
  # Analysis parameters
  temperature: 0.3  # Lower temperature for more consistent analysis
  max_tokens: 2000
  
  # Rate limiting
  requests_per_minute: 20
  batch_size: 5
  
  # Cost control
  max_cost_per_day: 25.0  # USD - reduced as gpt-4o-mini is more cost-effective
  enable_cost_tracking: true
  
  # Analysis prompts
  system_prompt_version: "v1.0"
  enable_scam_detection: true
  enable_sentiment_analysis: true
  enable_technical_analysis: true

# Token Evaluation Settings
evaluation:
  # Scoring weights for different components
  scoring_weights:
    ai_analysis: 0.35
    social_sentiment: 0.25
    engagement_metrics: 0.20
    technical_indicators: 0.15
    risk_assessment: -0.05  # Negative weight (penalty)
  
  # Risk assessment thresholds
  risk_thresholds:
    low: 20
    medium: 50
    high: 75
    critical: 90
  
  # Filtering criteria
  filters:
    min_engagement_score: 0.01
    min_sentiment_score: -0.5
    max_risk_score: 80
    min_confidence: 0.3

# Integration with Asset Rotation Strategy
integration:
  # Automatic addition thresholds
  auto_add_threshold: 80  # Score threshold for automatic addition
  max_auto_additions: 3   # Maximum automatic additions per cycle
  
  # Manual review thresholds
  review_required_threshold: 65
  
  # GeckoTerminal configuration
  geckoterminal_config_path: "config/geckoterminal_tokens.yaml"
  
  # Token management
  max_discovered_tokens: 100  # Maximum tokens to track
  cleanup_after_days: 30      # Remove old rejected tokens
  
  # Approval workflow
  require_manual_approval: true
  approval_timeout_hours: 48

# Database Configuration
database:
  # Database file path
  path: "data/memecoin_discovery.db"
  
  # Backup settings
  enable_backups: true
  backup_frequency_hours: 24
  max_backup_files: 7
  
  # Performance settings
  enable_indexing: true
  vacuum_frequency_days: 7

# Notification Settings
notifications:
  # Enable/disable notifications
  enabled: true
  
  # Notification types
  discovery_alerts: true
  integration_alerts: true
  error_alerts: true
  daily_summary: true
  
  # Telegram bot settings (uses same bot as memecoin strategy)
  telegram:
    enabled: true
    use_memecoin_bot: true  # Use the memecoin strategy Telegram bot
    
  # Notification thresholds
  high_score_threshold: 85
  scam_risk_threshold: 70
  
  # Frequency limits
  max_notifications_per_hour: 10
  summary_time: "23:50"  # Daily summary time (UTC)

# Logging Configuration
logging:
  level: "INFO"
  file: "logs/memecoin_discovery.log"
  max_file_size_mb: 50
  backup_count: 5
  
  # Console output
  console_output: true
  detailed_logs: true
  
  # Component-specific logging
  components:
    twitter_scraper: "INFO"
    ai_analyzer: "INFO"
    token_evaluator: "INFO"
    discovery_engine: "INFO"
    integration: "INFO"

# Performance and Resource Management
performance:
  # Threading settings
  max_worker_threads: 4
  
  # Memory management
  max_memory_usage_mb: 1024
  enable_memory_monitoring: true
  
  # Cache settings
  enable_caching: true
  cache_ttl_hours: 6
  max_cache_size_mb: 256
  
  # Rate limiting
  global_rate_limit: 100  # requests per minute across all APIs

# Security Settings
security:
  # API key validation
  validate_api_keys: true
  
  # Data sanitization
  sanitize_social_data: true
  remove_personal_info: true
  
  # Access control
  enable_api_authentication: false  # For future web interface
  
  # Data retention
  max_data_retention_days: 90
  anonymize_old_data: true

# Development and Testing
development:
  # Test mode settings
  enable_test_mode: false
  use_mock_apis: false
  
  # Debug settings
  debug_mode: false
  save_raw_responses: false
  
  # Simulation settings
  simulate_discoveries: false
  mock_data_path: "test_data/mock_discoveries.json"

# Monitoring and Metrics
monitoring:
  # Performance metrics
  track_performance: true
  metrics_retention_days: 30
  
  # Success metrics
  track_success_rate: true
  track_prediction_accuracy: true
  
  # API usage tracking
  track_api_usage: true
  track_costs: true
  
  # Health checks
  enable_health_checks: true
  health_check_interval_minutes: 5

# Backup and Recovery
backup:
  # Database backups
  enable_database_backup: true
  backup_schedule: "daily"
  backup_retention_days: 30
  
  # Configuration backups
  backup_configs: true
  
  # Recovery settings
  enable_auto_recovery: true
  max_recovery_attempts: 3
