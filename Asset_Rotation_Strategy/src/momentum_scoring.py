# src/momentum_scoring.py
# Implementation of momentum-based asset selection logic

import logging
from typing import Dict, List, Optional, Tu<PERSON>

def find_best_asset_with_momentum(
    current_scores: Dict[str, int], 
    previous_scores: Dict[str, int], 
    mtpi_signal: Optional[int] = None
) -> str:
    """
    Determines the best asset based on scores and momentum for a single day.
    
    Args:
        current_scores: A dictionary {asset_symbol: score} for the current day.
        previous_scores: A dictionary {asset_symbol: score} for the previous day.
        mtpi_signal: Optional MTPI signal value. If provided and not 1 (bullish),
                    returns an empty string to indicate no asset should be selected.
    
    Returns:
        The symbol of the asset with the highest score, using momentum for tie-breaking.
        Returns empty string if mtpi_signal is provided and not bullish (1).
    """
    logging.debug(f"[MOMENTUM] find_best_asset_with_momentum called")
    logging.debug(f"[MOMENTUM] Current scores: {current_scores}")
    logging.debug(f"[MOMENTUM] Previous scores: {previous_scores}")
    
    # Check if MTPI signal is provided and not bullish
    if mtpi_signal is not None and mtpi_signal != 1:
        logging.info(f"MTPI signal is {mtpi_signal}, staying out of the market")
        return ''
    
    if not current_scores:
        logging.warning("No current scores provided, returning empty string")
        return ''
    
    # Find the maximum score among all assets
    max_score = max(current_scores.values())
    logging.debug(f"[MOMENTUM] Maximum score found: {max_score}")
    
    # If all scores are 0 and MTPI signal is not explicitly bullish, stay out of the market
    if max_score == 0 and (mtpi_signal is None or mtpi_signal != 1):
        logging.info("All asset scores are 0, staying out of the market")
        return ''
    
    # Get all assets with the maximum score
    top_assets = [asset for asset, score in current_scores.items() if score == max_score]
    logging.debug(f"[MOMENTUM] Assets with max score {max_score}: {top_assets}")
    
    if len(top_assets) == 1:
        # Clear winner - no tie-breaking needed
        best_asset = top_assets[0]
        logging.debug(f"[MOMENTUM] Clear winner: {best_asset}")
        return best_asset
    
    # Tie situation - apply momentum logic
    logging.info(f"[MOMENTUM] TIE DETECTED: {len(top_assets)} assets tied at score {max_score}")
    logging.info(f"[MOMENTUM] Tied assets: {top_assets}")
    
    # Calculate momentum for tied assets
    momentum_analysis = {}
    for asset in top_assets:
        prev_score = previous_scores.get(asset, 0)
        current_score = current_scores[asset]
        momentum = current_score - prev_score
        momentum_analysis[asset] = {
            'prev_score': prev_score,
            'current_score': current_score,
            'momentum': momentum
        }
        logging.info(f"[MOMENTUM] {asset}: {prev_score} -> {current_score} (Δ={momentum:+.1f})")
    
    # Find asset(s) with the best momentum
    best_momentum = max(data['momentum'] for data in momentum_analysis.values())
    best_momentum_assets = [asset for asset, data in momentum_analysis.items() 
                           if data['momentum'] == best_momentum]
    
    logging.info(f"[MOMENTUM] Best momentum (Δ={best_momentum:+.1f}): {best_momentum_assets}")
    
    if len(best_momentum_assets) == 1:
        # Clear momentum winner
        best_asset = best_momentum_assets[0]
        logging.info(f"[MOMENTUM] Momentum winner: {best_asset}")
        return best_asset
    
    # Still tied after momentum - use deterministic tie-breaker (asset order)
    logging.info(f"[MOMENTUM] Still tied after momentum analysis: {best_momentum_assets}")
    
    # Use predefined asset order for final tie-breaking
    asset_order = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']
    
    # Find the first asset in the predefined order that's in the tied list
    for asset in asset_order:
        if asset in best_momentum_assets:
            logging.info(f"[MOMENTUM] Final tie-breaker: Selected {asset} (first in order)")
            return asset
    
    # Fallback - should not happen if asset_order is comprehensive
    best_asset = best_momentum_assets[0]
    logging.warning(f"[MOMENTUM] Fallback tie-breaker: Selected {best_asset}")
    return best_asset


def find_top_n_assets_with_momentum(
    current_scores: Dict[str, int],
    previous_scores: Dict[str, int],
    n: int = 3,
    mtpi_signal: Optional[int] = None,
    current_holdings: Optional[List[str]] = None
) -> List[str]:
    """
    Determines the top N assets based on scores and momentum for a single day.

    Args:
        current_scores: A dictionary {asset_symbol: score} for the current day.
        previous_scores: A dictionary {asset_symbol: score} for the previous day.
        n: Number of top assets to select (default: 3)
        mtpi_signal: Optional MTPI signal value. If provided and not 1 (bullish),
                    returns an empty list to indicate no assets should be selected.
        current_holdings: Optional list of currently held assets. When momentum is tied,
                         prefer the incumbent (currently held asset).

    Returns:
        A list of symbols of the top N assets, using momentum for tie-breaking.
        Returns empty list if mtpi_signal is provided and not bullish (1).
    """
    # logging.error(f"🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n={n}")
    # logging.error(f"🚨 [MOMENTUM] Current scores: {current_scores}")
    # logging.error(f"🚨 [MOMENTUM] Previous scores: {previous_scores}")

    # Check if MTPI signal is provided and not bullish
    if mtpi_signal is not None and mtpi_signal != 1:
        return []
    
    if not current_scores:
        return []
    
    # Calculate momentum for all assets
    asset_data = []
    for asset, current_score in current_scores.items():
        prev_score = previous_scores.get(asset, 0)
        momentum = current_score - prev_score
        asset_data.append({
            'asset': asset,
            'current_score': current_score,
            'prev_score': prev_score,
            'momentum': momentum
        })
    
    # Sort by score first (descending), then by momentum (descending), then by incumbent preference, then by asset order
    asset_order = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']

    def sort_key(data):
        asset = data['asset']
        order_index = asset_order.index(asset) if asset in asset_order else len(asset_order)

        # Prefer incumbent when momentum is tied (lower value = higher priority)
        is_incumbent = 0 if current_holdings and asset in current_holdings else 1

        return (-data['current_score'], -data['momentum'], is_incumbent, order_index)
    
    sorted_assets = sorted(asset_data, key=sort_key)
    
    # Log the sorting for debugging
    # logging.debug(f"[MOMENTUM] Asset ranking with momentum:")
    # for i, data in enumerate(sorted_assets[:min(n+2, len(sorted_assets))], 1):
    #     logging.debug(f"[MOMENTUM]   {i}. {data['asset']}: score={data['current_score']}, "
    #                  f"momentum={data['momentum']:+.1f} ({data['prev_score']}->{data['current_score']})")

    # Select top N assets
    top_assets = [data['asset'] for data in sorted_assets[:n]]

    # logging.info(f"[MOMENTUM] Selected top {len(top_assets)} assets: {top_assets}")
    return top_assets


def find_top_n_assets_with_momentum_and_weights(
    current_scores: Dict[str, int],
    previous_scores: Dict[str, int],
    n: int = 3,
    mtpi_signal: Optional[int] = None,
    weights: Optional[List[float]] = None,
    current_holdings: Optional[List[str]] = None
) -> Tuple[List[str], Dict[str, float]]:
    """
    Determines the top N assets based on scores and momentum, and assigns weights.

    Args:
        current_scores: A dictionary {asset_symbol: score} for the current day.
        previous_scores: A dictionary {asset_symbol: score} for the previous day.
        n: Number of top assets to select (default: 3)
        mtpi_signal: Optional MTPI signal value. If provided and not 1 (bullish),
                    returns empty lists to indicate no assets should be selected.
        weights: Optional list of weights for the top N assets.
        current_holdings: Optional list of currently held assets. When momentum is tied,
                         prefer the incumbent (currently held asset).

    Returns:
        A tuple containing:
        - A list of symbols of the top N assets
        - A dictionary mapping each asset to its allocation weight
    """
    # Get top assets using momentum logic
    top_assets = find_top_n_assets_with_momentum(current_scores, previous_scores, n, mtpi_signal, current_holdings)
    
    if not top_assets:
        return [], {}
    
    # Determine weights
    if weights is None:
        if n == 3:
            weights = [0.7, 0.2, 0.1]  # Default weights for 3 assets
        else:
            # Equal weights for other values of n
            weight = 1.0 / n
            weights = [weight] * n
    
    # Create asset weights dictionary
    asset_weights = {}
    for i, asset in enumerate(top_assets):
        if i < len(weights):
            asset_weights[asset] = weights[i]
        else:
            # If we have more assets than weights, distribute remaining weight equally
            remaining_weight = 1.0 - sum(weights)
            remaining_assets = len(top_assets) - len(weights)
            if remaining_assets > 0:
                asset_weights[asset] = remaining_weight / remaining_assets
            else:
                asset_weights[asset] = 0.0
    
    logging.info(f"[MOMENTUM] Asset weights: {asset_weights}")
    return top_assets, asset_weights
