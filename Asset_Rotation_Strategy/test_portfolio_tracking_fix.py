#!/usr/bin/env python3
"""
Test script to verify the portfolio tracking fixes work correctly.
This simulates the scenario where XRP was sold but not properly tracked.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading.executor import TradingExecutor
from src.trading.paper_trading import PaperTrading
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_portfolio_tracking_fix():
    """Test the portfolio tracking fix for the XRP/ETH scenario."""
    
    print("=" * 80)
    print("TESTING PORTFOLIO TRACKING FIX")
    print("=" * 80)
    
    # Create a mock trading config
    trading_config = {
        'mode': 'paper',
        'enabled': True,
        'exchange': 'kraken',
        'assets': ['XRP/EUR', 'ETH/EUR', 'DOGE/EUR']
    }
    
    # Initialize paper trading with some initial balance
    paper_trading = PaperTrading(initial_balance={'EUR': 5000.0})
    
    # Create executor
    executor = TradingExecutor('kraken', trading_config=trading_config)
    executor.paper_trading = paper_trading
    
    print("\n1. INITIAL STATE:")
    print(f"   Available EUR: {paper_trading.get_balance()['EUR']:.2f}")
    print(f"   Portfolio tracking: {executor.current_portfolio}")
    
    # Simulate having XRP and DOGE positions (50-50 split)
    print("\n2. SIMULATING INITIAL POSITIONS:")
    
    # Add XRP position to paper trading
    paper_trading.positions['XRP/EUR'] = {
        'amount': 1521.080208,
        'entry_price': 2.5,
        'entry_time': '2025-07-23T00:00:00',
        'cost': 3802.70,
        'current_price': 2.69953,
        'current_value': 4106.33,
        'pnl': 303.63,
        'pnl_pct': 7.99
    }
    
    # Add DOGE position to paper trading  
    paper_trading.positions['DOGE/EUR'] = {
        'amount': 2450.0,
        'entry_price': 0.20,
        'entry_time': '2025-07-23T00:00:00',
        'cost': 490.0,
        'current_price': 0.204,
        'current_value': 499.8,
        'pnl': 9.8,
        'pnl_pct': 2.0
    }
    
    # Update executor's portfolio tracking
    executor.current_portfolio = {'XRP/EUR': 0.5, 'DOGE/EUR': 0.5}
    
    # Reduce available balance since we have positions
    paper_trading.balances['EUR'] = 393.87  # Remaining balance after positions
    
    print(f"   XRP position: {paper_trading.positions['XRP/EUR']['amount']:.6f} units @ {paper_trading.positions['XRP/EUR']['current_price']:.5f}")
    print(f"   DOGE position: {paper_trading.positions['DOGE/EUR']['amount']:.6f} units @ {paper_trading.positions['DOGE/EUR']['current_price']:.5f}")
    print(f"   Portfolio tracking: {executor.current_portfolio}")
    print(f"   Available EUR: {paper_trading.get_balance()['EUR']:.2f}")
    
    print("\n3. SIMULATING XRP ALREADY SOLD (but not tracked):")
    
    # Remove XRP position from paper trading (simulating it was already sold)
    xrp_position = paper_trading.positions.pop('XRP/EUR')
    xrp_proceeds = xrp_position['amount'] * xrp_position['current_price']
    paper_trading.balances['EUR'] += xrp_proceeds
    
    print(f"   Removed XRP position from exchange")
    print(f"   Added {xrp_proceeds:.2f} EUR to balance")
    print(f"   Available EUR: {paper_trading.get_balance()['EUR']:.2f}")
    print(f"   Portfolio tracking still shows: {executor.current_portfolio}")
    
    print("\n4. TESTING NEW TARGET ALLOCATION (DOGE 50%, ETH 50%):")
    
    new_allocation = {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
    
    # Mock current prices
    def mock_get_current_price(symbol):
        prices = {
            'XRP/EUR': 2.69953,
            'DOGE/EUR': 0.204,
            'ETH/EUR': 3078.90
        }
        return prices.get(symbol, 0.0)
    
    executor.get_current_price = mock_get_current_price
    
    print(f"   Target allocation: {new_allocation}")
    print(f"   Current prices: XRP/EUR={mock_get_current_price('XRP/EUR')}, DOGE/EUR={mock_get_current_price('DOGE/EUR')}, ETH/EUR={mock_get_current_price('ETH/EUR')}")
    
    print("\n5. EXECUTING MULTI-ASSET STRATEGY:")
    
    # Execute the strategy
    result = executor.execute_multi_asset_strategy(new_allocation, mtpi_signal=1)
    
    print(f"   Execution result: {result.get('success', False)}")
    print(f"   Reason: {result.get('reason', 'N/A')}")
    
    if 'trades' in result:
        print(f"   Number of trades: {len(result['trades'])}")
        for i, trade in enumerate(result['trades']):
            print(f"     Trade {i+1}: {trade.get('side', 'unknown')} {trade.get('symbol', 'unknown')} - Success: {trade.get('success', False)}")
            if 'reason' in trade:
                print(f"       Reason: {trade['reason']}")
            if 'proceeds' in trade:
                print(f"       Proceeds: {trade['proceeds']:.2f}")
    
    print("\n6. FINAL STATE:")
    print(f"   Portfolio tracking: {executor.current_portfolio}")
    print(f"   Available EUR: {paper_trading.get_balance()['EUR']:.2f}")
    print(f"   Positions:")
    for symbol, position in paper_trading.get_positions().items():
        print(f"     {symbol}: {position['amount']:.6f} units @ {position['current_price']:.5f} = {position['current_value']:.2f} EUR")
    
    # Calculate total portfolio value
    total_value = paper_trading.get_balance()['EUR']
    for position in paper_trading.get_positions().values():
        total_value += position['current_value']
    
    print(f"   Total portfolio value: {total_value:.2f} EUR")
    
    print("\n" + "=" * 80)
    print("TEST COMPLETED")
    print("=" * 80)
    
    return result

if __name__ == "__main__":
    test_portfolio_tracking_fix()
